/**
 * AI 源管理器
 * 兼容 fount 平台的 AIsources_manager 实现
 */

import fs from 'node:fs'
import path from 'node:path'
import { loadJsonFileIfExists, saveJsonFile } from './json_loader.mjs'
import { structPromptToSingleNoChatLog, margeStructPromptChatLog } from './prompt_struct.mjs'

/** @typedef {import('../types/AIsource.mjs').AIsource_t} AIsource_t */
/** @typedef {import('../types/AIsource.mjs').AISourceConfig_t} AISourceConfig_t */
/** @typedef {import('../types/AIsource.mjs').AISourceRequest_t} AISourceRequest_t */
/** @typedef {import('../types/AIsource.mjs').AISourceResponse_t} AISourceResponse_t */
/** @typedef {import('./prompt_struct.mjs').prompt_struct_t} prompt_struct_t */

/**
 * AI 源管理器类
 */
class AISourcesManager {
	constructor() {
		/** @type {Map<string, AIsource_t>} */
		this.sources = new Map()
		this.configDir = './config/aisources'
		this.ensureConfigDir()
	}

	/**
	 * 确保配置目录存在
	 */
	ensureConfigDir() {
		if (!fs.existsSync(this.configDir)) {
			fs.mkdirSync(this.configDir, { recursive: true })
		}
	}

	/**
	 * 加载 AI 源
	 * @param {string} filename - 配置文件名
	 * @returns {Promise<AIsource_t|null>} AI 源实例
	 */
	async loadAIsource(filename) {
		try {
			const configPath = path.join(this.configDir, filename)
			const config = loadJsonFileIfExists(configPath)

			if (!config) {
				console.warn(`AI source config not found: ${filename}`)
				return null
			}

			const source = this.createAISource(config, filename)

			// 尝试加载对应的fount AI源生成器
			await this.loadFountGenerator(source)

			this.sources.set(filename, source)
			return source
		} catch (error) {
			console.error(`Failed to load AI source ${filename}:`, error.message)
			return null
		}
	}

	/**
	 * 加载fount AI源生成器
	 * @param {AIsource_t} source - AI源实例
	 */
	async loadFountGenerator(source) {
		try {
			const config = source.config
			let generatorPath = null

			// 根据AI源类型确定生成器路径
			switch (config.type) {
				case 'gemini':
					generatorPath = './fount_ai_generators/gemini/main.mjs'
					break
				case 'openai':
				case 'claude':
				case 'anthropic':
					generatorPath = './fount_ai_generators/proxy/main.mjs'
					break
				default:
					// 不支持的类型，跳过fount生成器加载
					return
			}

			if (generatorPath) {
				const generatorModule = await import(generatorPath)
				if (generatorModule.default?.interfaces?.AIsource) {
					// 创建兼容的配置对象，确保参数映射正确
					const fountCompatibleConfig = {
						...config,
						// 确保URL参数正确映射
						url: config.endpoint || config.url,
						// 确保API密钥参数正确映射
						apikey: config.apiKey || config.apikey,
						// 确保模型参数正确映射
						model: config.model,
						// 映射参数结构
						model_arguments: {
							...config.model_arguments,
							temperature: config.parameters?.temperature || config.model_arguments?.temperature,
							top_p: config.parameters?.top_p || config.model_arguments?.top_p,
							max_tokens: config.parameters?.max_tokens || config.maxTokens || config.model_arguments?.max_tokens,
							topP: config.parameters?.topP || config.model_arguments?.topP,
							topK: config.parameters?.topK || config.model_arguments?.topK,
							maxOutputTokens: config.parameters?.maxOutputTokens || config.maxTokens || config.model_arguments?.maxOutputTokens,
							responseMimeType: config.parameters?.responseMimeType || config.model_arguments?.responseMimeType,
							responseModalities: config.parameters?.responseModalities || config.model_arguments?.responseModalities
						}
					}

					const fountSource = await generatorModule.default.interfaces.AIsource.GetSource(
						fountCompatibleConfig,
						{
							SaveConfig: (newConfig) => {
								Object.assign(source.config, newConfig)
								const configPath = path.join(this.configDir, source.filename)
								saveJsonFile(configPath, source.config)
							}
						}
					)
					source.fountGenerator = fountSource
					console.log(`🔗 Loaded fount generator for ${source.name}`)
				}
			}
		} catch (error) {
			console.warn(`Failed to load fount generator for ${source.name}:`, error.message)
			// 继续使用传统API调用方式
		}
	}

	/**
	 * 创建 AI 源实例 (兼容 fount 平台)
	 * @param {AISourceConfig_t} config - 配置
	 * @param {string} filename - 文件名
	 * @returns {AIsource_t} AI 源实例
	 */
	createAISource(config, filename) {
		return {
			filename,
			name: config.name || filename,
			description: config.description || '',
			type: 'text-chat',
			info: {
				'': {
					avatar: '',
					name: config.name || filename,
					provider: config.type || 'unknown',
					description: config.description || '',
					description_markdown: config.description || '',
					version: '1.0.0',
					author: 'GentianAphrodite',
					homepage: '',
					tags: [config.type || 'ai'],
				}
			},
			is_paid: config.is_paid || false,
			extension: {},
			config,
			status: {
				available: true,
				status: 'ready',
				lastUsed: 0,
				requestCount: 0,
				errorCount: 0,
				lastError: ''
			},

			// fount 平台兼容方法
			Unload: async () => { },
			Call: async (prompt) => this.callAISourceWithPrompt(filename, prompt),
			StructCall: async (prompt_struct) => this.callAISourceWithStruct(filename, prompt_struct),
			Tokenizer: {
				free: async () => { },
				encode: (prompt) => prompt.split(' '), // 简单实现
				decode: (tokens) => tokens.join(' '), // 简单实现
				decode_single: (token) => token,
				get_token_count: (prompt) => Math.ceil(prompt.length / 4), // 简单估算
			},

			// 原有方法
			call: async (request) => this.callAISource(filename, request),
			test: async () => this.testAISource(filename),
			getInfo: () => this.getAISourceInfo(filename),
			updateConfig: (newConfig) => this.updateAISourceConfig(filename, newConfig),
			reset: () => this.resetAISourceStatus(filename)
		}
	}

	/**
	 * 使用提示词调用 AI 源 (fount 平台兼容)
	 * @param {string} filename - AI 源文件名
	 * @param {string} prompt - 提示词
	 * @returns {Promise<{content: string}>} 响应
	 */
	async callAISourceWithPrompt(filename, prompt) {
		const source = this.sources.get(filename)
		if (!source) {
			throw new Error(`AI source not found: ${filename}`)
		}

		try {
			source.status.lastUsed = Date.now()
			source.status.requestCount++

			// 模拟AI调用
			const response = await this.performAICallWithPrompt(source, prompt)
			return response
		} catch (error) {
			source.status.errorCount++
			source.status.lastError = error.message
			throw error
		}
	}

	/**
	 * 使用结构化提示词调用 AI 源 (fount 平台兼容)
	 * @param {string} filename - AI 源文件名
	 * @param {prompt_struct_t} prompt_struct - 结构化提示词
	 * @returns {Promise<{content: string, files?: any[]}>} 响应
	 */
	async callAISourceWithStruct(filename, prompt_struct) {
		console.log(`[AIS_Mgr_Debug] callAISourceWithStruct: Attempting to get source with filename: '${filename}'. Available keys in manager: ${JSON.stringify(Array.from(this.sources.keys()))}`);
		const source = this.sources.get(filename)
		if (!source) {
			throw new Error(`AI source not found: ${filename}`)
		}

		try {
			source.status.lastUsed = Date.now()
			source.status.requestCount++

			// 将结构化提示词转换为消息格式
			const messages = this.convertStructToMessages(prompt_struct)
			const response = await this.performAICallWithMessages(source, messages)
			return response
		} catch (error) {
			source.status.errorCount++
			source.status.lastError = error.message
			throw error
		}
	}

	/**
	 * 调用 AI 源 (原有方法)
	 * @param {string} filename - AI 源文件名
	 * @param {AISourceRequest_t} request - 请求
	 * @returns {Promise<AISourceResponse_t>} 响应
	 */
	async callAISource(filename, request) {
		const source = this.sources.get(filename)
		if (!source) {
			throw new Error(`AI source not found: ${filename}`)
		}

		try {
			source.status.lastUsed = Date.now()
			source.status.requestCount++

			// TODO: 实现实际的 AI 调用逻辑
			// 这里需要根据不同的 AI 源类型实现具体的调用逻辑
			const response = await this.performAICall(source, request)

			return response
		} catch (error) {
			source.status.errorCount++
			source.status.lastError = error.message
			throw error
		}
	}

	/**
	 * 将结构化提示词转换为消息格式
	 * @param {prompt_struct_t} prompt_struct - 结构化提示词
	 * @returns {Object[]} 消息数组
	 */
	convertStructToMessages(prompt_struct) {
		const messages = []

		// 添加系统提示词
		const systemPrompt = structPromptToSingleNoChatLog(prompt_struct)
		if (systemPrompt) {
			messages.push({
				role: 'system',
				content: systemPrompt
			})
		}

		// 添加聊天历史
		const chatLog = margeStructPromptChatLog(prompt_struct)
		for (const entry of chatLog) {
			messages.push({
				role: entry.role === 'user' ? 'user' : entry.role === 'system' ? 'system' : 'assistant',
				content: entry.content
			})
		}

		return messages
	}

	/**
	 * 执行基于提示词的 AI 调用
	 * @param {AIsource_t} source - AI 源
	 * @param {string} prompt - 提示词
	 * @returns {Promise<{content: string}>} 响应
	 */
	async performAICallWithPrompt(source, prompt) {
		// 将提示词转换为消息格式
		const messages = [{
			role: 'user',
			content: prompt
		}]

		// 调用基于消息的API
		const result = await this.performAICallWithMessages(source, messages)

		return {
			content: result.content
		}
	}

	/**
	 * 执行基于消息的 AI 调用
	 * @param {AIsource_t} source - AI 源
	 * @param {Object[]} messages - 消息数组
	 * @returns {Promise<{content: string, files?: any[]}>} 响应
	 */
	async performAICallWithMessages(source, messages) {
		const config = source.config
		const maxRetries = 3
		const retryDelay = 1000 // 1秒

		for (let attempt = 1; attempt <= maxRetries; attempt++) {
			try {
				// 使用fount AI源生成器进行真实API调用
				if (source.fountGenerator) {
					const result = await source.fountGenerator.Call(messages[messages.length - 1]?.content || '')
					return {
						content: result.content,
						files: result.files || []
					}
				}

				// 回退到传统API调用方式
				switch (config.type) {
					case 'openai':
						return await this.callOpenAIAPI(config, messages)
					case 'anthropic':
					case 'claude':
						return await this.callAnthropicAPI(config, messages)
					case 'gemini':
						return await this.callGeminiAPI(config, messages)
					case 'local':
						return await this.callLocalAPI(config, messages)
					default:
						console.warn(`Unsupported AI source type: ${config.type}`)
						// 回退到模拟响应
						const lastMessage = messages[messages.length - 1]
						return {
							content: `不支持的AI源类型 "${config.type}"。收到 ${messages.length} 条消息，最后一条是 "${lastMessage?.content?.substring(0, 50) || '空'}..."。`,
							files: []
						}
				}
			} catch (error) {
				console.error(`AI call attempt ${attempt}/${maxRetries} failed:`, error.message)

				// 如果是最后一次尝试，抛出错误
				if (attempt === maxRetries) {
					throw error
				}

				// 等待后重试
				await new Promise(resolve => setTimeout(resolve, retryDelay * attempt))
			}
		}
	}

	/**
	 * 调用 OpenAI 兼容的 API
	 * @param {AISourceConfig_t} config - AI 源配置
	 * @param {Object[]} messages - 消息数组
	 * @returns {Promise<{content: string, files?: any[]}>} 响应
	 */
	async callOpenAIAPI(config, messages) {
		let url = config.endpoint || 'https://api.openai.com/v1'

		// 确保URL以正确的端点结尾
		if (!url.endsWith('/chat/completions')) {
			if (!url.endsWith('/')) {
				url += '/'
			}
			if (!url.endsWith('v1/')) {
				url += 'v1/'
			}
			url += 'chat/completions'
		}

		const apiKey = config.apiKey

		if (!apiKey) {
			throw new Error(`OpenAI API key not configured for ${config.name}`)
		}

		const requestBody = {
			model: config.model || 'gpt-3.5-turbo',
			messages: messages,
			temperature: config.parameters?.temperature || config.temperature || 0.7,
			max_tokens: config.parameters?.max_tokens || config.maxTokens || 2000,
			top_p: config.parameters?.top_p || config.topP || 0.9,
			frequency_penalty: config.parameters?.frequency_penalty || config.frequencyPenalty || 0,
			presence_penalty: config.parameters?.presence_penalty || config.presencePenalty || 0,
			stream: false
		}

		try {
			const response = await fetch(url, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${apiKey}`,
					'User-Agent': 'GentianAphrodite/1.0'
				},
				body: JSON.stringify(requestBody)
			})

			if (!response.ok) {
				const errorText = await response.text()
				let errorMessage = `OpenAI API error: ${response.status} ${response.statusText}`

				try {
					const errorJson = JSON.parse(errorText)
					if (errorJson.error?.message) {
						errorMessage = errorJson.error.message
					}
				} catch {
					errorMessage += ` - ${errorText}`
				}

				throw new Error(errorMessage)
			}

			const data = await response.json()

			if (!data.choices || data.choices.length === 0) {
				throw new Error('No response choices returned from OpenAI API')
			}

			const choice = data.choices[0]
			const content = choice.message?.content || choice.text || ''

			return {
				content: content,
				files: []
			}
		} catch (error) {
			console.error('OpenAI API call failed:', error)
			throw error
		}
	}

	/**
	 * 调用 Anthropic Claude API
	 * @param {AISourceConfig_t} config - AI 源配置
	 * @param {Object[]} messages - 消息数组
	 * @returns {Promise<{content: string, files?: any[]}>} 响应
	 */
	async callAnthropicAPI(config, messages) {
		let url = config.endpoint || 'https://api.anthropic.com/v1/messages'
		const apiKey = config.apiKey

		// 检测是否是OpenAI兼容的端点（如tbai.xin）
		if (url.includes('tbai.xin') || url.includes('openai') || !url.includes('anthropic')) {
			console.warn(`Claude type AI source using OpenAI-compatible endpoint: ${url}. Switching to OpenAI API call.`)
			return await this.callOpenAIAPI(config, messages)
		}

		// 确保URL以正确的端点结尾
		if (!url.endsWith('/messages')) {
			if (!url.endsWith('/')) {
				url += '/'
			}
			if (!url.endsWith('v1/')) {
				url += 'v1/'
			}
			url += 'messages'
		}

		if (!apiKey) {
			throw new Error(`Anthropic API key not configured for ${config.name}`)
		}

		// 转换消息格式 - Anthropic需要分离system消息
		const systemMessages = messages.filter(m => m.role === 'system')
		const conversationMessages = messages.filter(m => m.role !== 'system')

		const systemPrompt = systemMessages.map(m => m.content).join('\n\n')

		const requestBody = {
			model: config.model || 'claude-3-sonnet-20240229',
			max_tokens: config.parameters?.max_tokens || config.maxTokens || 2000,
			temperature: config.parameters?.temperature || config.temperature || 0.7,
			top_p: config.parameters?.top_p || config.topP || 0.9,
			messages: conversationMessages,
			stream: false
		}

		// 如果有system prompt，添加到请求中
		if (systemPrompt) {
			requestBody.system = systemPrompt
		}

		try {
			const response = await fetch(url, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'x-api-key': apiKey,
					'anthropic-version': '2023-06-01',
					'User-Agent': 'GentianAphrodite/1.0'
				},
				body: JSON.stringify(requestBody)
			})

			if (!response.ok) {
				const errorText = await response.text()
				let errorMessage = `Anthropic API error: ${response.status} ${response.statusText}`

				try {
					const errorJson = JSON.parse(errorText)
					if (errorJson.error?.message) {
						errorMessage = errorJson.error.message
					}
				} catch {
					errorMessage += ` - ${errorText}`
				}

				throw new Error(errorMessage)
			}

			const data = await response.json()

			if (!data.content || data.content.length === 0) {
				throw new Error('No content returned from Anthropic API')
			}

			// Anthropic返回的content是数组格式
			const content = data.content
				.filter(item => item.type === 'text')
				.map(item => item.text)
				.join('')

			return {
				content: content,
				files: []
			}
		} catch (error) {
			console.error('Anthropic API call failed:', error)
			throw error
		}
	}

	/**
	 * 调用 Gemini API
	 * @param {AISourceConfig_t} config - AI 源配置
	 * @param {Object[]} messages - 消息数组
	 * @returns {Promise<{content: string, files?: any[]}>} 响应
	 */
	async callGeminiAPI(config, messages) {
		const url = config.endpoint || 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent'

		// 转换消息格式为Gemini格式
		const parts = []
		for (const message of messages) {
			if (message.role === 'system' || message.role === 'user') {
				parts.push({ text: message.content })
			}
		}

		const requestBody = {
			contents: [{
				parts: parts
			}],
			generationConfig: {
				temperature: config.parameters?.temperature || config.temperature || 0.7,
				topP: config.parameters?.top_p || config.topP || 0.9,
				maxOutputTokens: config.parameters?.max_tokens || config.maxTokens || 2000
			}
		}

		try {
			const response = await fetch(`${url}?key=${config.apiKey}`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'User-Agent': 'GentianAphrodite/1.0'
				},
				body: JSON.stringify(requestBody)
			})

			if (!response.ok) {
				const errorText = await response.text()
				throw new Error(`Gemini API error: ${response.status} ${response.statusText} - ${errorText}`)
			}

			const data = await response.json()
			const content = data.candidates?.[0]?.content?.parts?.[0]?.text || ''

			return {
				content,
				files: []
			}
		} catch (error) {
			console.error('Gemini API call failed:', error)
			throw error
		}
	}

	/**
	 * 调用本地 API
	 * @param {AISourceConfig_t} config - AI 源配置
	 * @param {Object[]} messages - 消息数组
	 * @returns {Promise<{content: string, files?: any[]}>} 响应
	 */
	async callLocalAPI(config, messages) {
		const url = config.endpoint || 'http://localhost:11434/api/chat'

		// 本地API通常使用Ollama格式
		const requestBody = {
			model: config.model || 'llama2',
			messages: messages,
			stream: false,
			options: {
				temperature: config.parameters?.temperature || config.temperature || 0.7,
				top_p: config.parameters?.top_p || config.topP || 0.9,
				num_predict: config.parameters?.max_tokens || config.maxTokens || 2000
			}
		}

		try {
			const response = await fetch(url, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'User-Agent': 'GentianAphrodite/1.0'
				},
				body: JSON.stringify(requestBody)
			})

			if (!response.ok) {
				const errorText = await response.text()
				throw new Error(`Local API error: ${response.status} ${response.statusText} - ${errorText}`)
			}

			const data = await response.json()

			// Ollama格式的响应
			const content = data.message?.content || data.response || ''

			return {
				content: content,
				files: []
			}
		} catch (error) {
			console.error('Local API call failed:', error)
			throw error
		}
	}

	/**
	 * 执行实际的 AI 调用 (原有方法)
	 * @param {AIsource_t} source - AI 源
	 * @param {AISourceRequest_t} request - 请求
	 * @returns {Promise<AISourceResponse_t>} 响应
	 */
	async performAICall(source, request) {
		// 将原有请求格式转换为消息格式
		const messages = []

		if (request.prompt) {
			messages.push({
				role: 'user',
				content: request.prompt
			})
		}

		if (request.messages) {
			messages.push(...request.messages)
		}

		// 调用新的消息格式API
		const result = await this.performAICallWithMessages(source, messages)

		// 转换回原有响应格式
		return {
			content: result.content,
			files: result.files || [],
			metadata: {
				source: source.filename,
				model: source.config.model || 'unknown'
			},
			tokensUsed: 0, // TODO: 实现token计算
			model: source.config.model || 'unknown',
			timestamp: Date.now()
		}
	}

	/**
	 * 测试 AI 源连接
	 * @param {string} filename - AI 源文件名
	 * @returns {Promise<boolean>} 是否连接成功
	 */
	async testAISource(filename) {
		try {
			const testRequest = {
				prompt: 'Test connection',
				messages: [],
				files: [],
				parameters: {},
				maxTokens: 10,
				temperature: 0.1,
				stream: false
			}
			
			await this.callAISource(filename, testRequest)
			return true
		} catch (error) {
			console.error(`AI source test failed for ${filename}:`, error.message)
			return false
		}
	}

	/**
	 * 获取 AI 源信息
	 * @param {string} filename - AI 源文件名
	 * @returns {Object} AI 源信息
	 */
	getAISourceInfo(filename) {
		const source = this.sources.get(filename)
		if (!source) {
			return null
		}

		return {
			filename: source.filename,
			name: source.name,
			description: source.description,
			type: source.config.type,
			model: source.config.model,
			status: source.status,
			enabled: source.config.enabled
		}
	}

	/**
	 * 更新 AI 源配置
	 * @param {string} filename - AI 源文件名
	 * @param {Partial<AISourceConfig_t>} newConfig - 新配置
	 * @returns {boolean} 是否更新成功
	 */
	updateAISourceConfig(filename, newConfig) {
		const source = this.sources.get(filename)
		if (!source) {
			return false
		}

		try {
			Object.assign(source.config, newConfig)
			const configPath = path.join(this.configDir, filename)
			saveJsonFile(configPath, source.config)
			return true
		} catch (error) {
			console.error(`Failed to update AI source config ${filename}:`, error.message)
			return false
		}
	}

	/**
	 * 重置 AI 源状态
	 * @param {string} filename - AI 源文件名
	 */
	resetAISourceStatus(filename) {
		const source = this.sources.get(filename)
		if (source) {
			source.status = {
				available: true,
				status: 'ready',
				lastUsed: 0,
				requestCount: 0,
				errorCount: 0,
				lastError: ''
			}
		}
	}

	/**
	 * 列出所有 AI 源
	 * @returns {string[]} AI 源文件名列表
	 */
	listSources() {
		return Array.from(this.sources.keys())
	}

	/**
	 * 获取 AI 源
	 * @param {string} filename - AI 源文件名
	 * @returns {AIsource_t|null} AI 源实例
	 */
	getSource(filename) {
		return this.sources.get(filename) || null
	}

	/**
	 * 卸载 AI 源
	 * @param {string} filename - AI 源文件名
	 * @returns {boolean} 是否卸载成功
	 */
	unloadSource(filename) {
		return this.sources.delete(filename)
	}

	/**
	 * 测试所有 AI 源
	 * @returns {Promise<Object>} 测试结果
	 */
	async testAllSources() {
		const results = {}
		for (const filename of this.sources.keys()) {
			results[filename] = await this.testAISource(filename)
		}
		return results
	}

    /**
     * 加载 AI 源直接从配置对象
     * @param {Array<{key: string, config: AISourceConfig_t}>} sourceConfigsWithKeys - 配置对象列表
     */
    async loadSourcesFromConfigObjects(sourceConfigsWithKeys) {
        console.log(`[AISManager] Attempting to load ${sourceConfigsWithKeys.length} AI sources directly from config objects.`);
        for (const { key, config } of sourceConfigsWithKeys) {
            if (!config.enabled) {
                console.log(`[AISManager] Skipping disabled AI Source: ${key}`);
                continue;
            }
            try {
                const source = this.createAISource(config, `unified:${key}`);
                await this.loadFountGenerator(source);
                this.sources.set(key, source);
                console.log(`[AISManager] Successfully loaded AI Source from unified config: ${key} (${config.name || 'Unnamed'})`);
            } catch (err) {
                console.error(`[AISManager] Failed to load AI Source ${key} from unified config. Error: ${err.message}`, err);
            }
        }
        const loadedCount = this.sources.size;
        console.log(`[AISManager] Finished loading. Total AI sources in manager: ${loadedCount}`);
    }
}

// 创建全局实例
const aiSourcesManager = new AISourcesManager()

/**
 * 加载 AI 源
 * @param {string} filename - 配置文件名
 * @returns {Promise<AIsource_t|null>} AI 源实例
 */
export function loadAIsource(filename) {
	return aiSourcesManager.loadAIsource(filename)
}

/**
 * 获取 AI 源管理器实例
 * @returns {AISourcesManager} 管理器实例
 */
export function getAISourcesManager() {
	return aiSourcesManager
}

export default aiSourcesManager
